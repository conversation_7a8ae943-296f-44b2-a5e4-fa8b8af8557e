#!/usr/bin/env bun
/**
 * 自动翻译脚本 - 将 en.json 翻译为多种语言
 * 使用 Pollinations AI API 进行翻译
 * 运行命令: bun run translate.js
 */

import fs from 'fs';
import path from 'path';

// API 配置
const API_URL = "https://text.pollinations.ai/openai";
const API_HEADERS = {
    "Content-Type": "application/json",
    "Authorization": "Bearer D5iEptuNtJWrRFjC"
};

// 目标语言配置
const LANGUAGES = {
    "ar": "Arabic",
    "bg": "Bulgarian", 
    "bn": "Bengali",
    "cs": "Czech",
    "da": "Danish",
    "de": "German",
    "el": "Greek",
    "es": "Spanish",
    "et": "Estonian",
    "fi": "Finnish",
    "fr": "French",
    "he": "Hebrew",
    "hi": "Hindi",
    "hr": "Croatian",
    "hu": "Hungarian",
    "id": "Indonesian",
    "it": "Italian",
    "ja": "Japanese",
    "ko": "Korean",
    "lt": "Lithuanian",
    "lv": "Latvian",
    "ms": "Malay",
    "nl": "Dutch",
    "no": "Norwegian",
    "pl": "Polish",
    "pt": "Portuguese",
    "pt-BR": "Brazilian Portuguese",
    "ro": "Romanian",
    "ru": "Russian",
    "sk": "Slovak",
    "sl": "Slovenian",
    "sv": "Swedish",
    "th": "Thai",
    "tr": "Turkish",
    "uk": "Ukrainian",
    "vi": "Vietnamese",
    "zh-CN": "Simplified Chinese",
    "zh-TW": "Traditional Chinese"
};

/**
 * 调用 AI API 进行翻译
 */
async function callAI(prompt, maxRetries = 3) {
    const payload = {
        model: "openai-fast",
        messages: [
            {
                role: "system", 
                content: "You are a professional translator. Translate the JSON content to the specified language. Keep the JSON structure unchanged, only translate the string values, not the keys. Ensure accurate, natural translations that fit the target language's expression habits. Return valid JSON format only."
            },
            {
                role: "user", 
                content: prompt
            }
        ],
        seed: 101
    };
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            const response = await fetch(API_URL, {
                method: 'POST',
                headers: API_HEADERS,
                body: JSON.stringify(payload)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            return result.choices[0].message.content;
            
        } catch (error) {
            console.log(`API call failed (attempt ${attempt + 1}/${maxRetries}): ${error.message}`);
            if (attempt < maxRetries - 1) {
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
            } else {
                throw error;
            }
        }
    }
}

/**
 * 翻译 JSON 内容
 */
async function translateJsonContent(content, targetLanguage, languageName) {
    console.log(`🔄 Translating to ${languageName} (${targetLanguage})...`);
    
    const jsonStr = JSON.stringify(content, null, 2);
    
    const prompt = `Translate the following JSON content to ${languageName}. Keep the JSON structure unchanged, only translate the string values:

${jsonStr}

Requirements:
1. Keep JSON structure completely unchanged
2. Only translate string values, not keys
3. Translations should be accurate, natural, and fit ${languageName} expression habits
4. Game category names should use common gaming terminology
5. Return complete JSON format only

Translated JSON:`;
    
    try {
        const translatedText = await callAI(prompt);
        
        // Clean up possible markdown formatting
        let cleanedText = translatedText;
        if (cleanedText.includes("```json")) {
            cleanedText = cleanedText.split("```json")[1].split("```")[0].trim();
        } else if (cleanedText.includes("```")) {
            cleanedText = cleanedText.split("```")[1].trim();
        }
        
        const translatedJson = JSON.parse(cleanedText);
        return translatedJson;
        
    } catch (error) {
        console.error(`❌ Translation failed for ${languageName}: ${error.message}`);
        return null;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log("🚀 Starting translation process...\n");
    
    // 检查源文件
    const sourceFile = "messages/en.json";
    if (!fs.existsSync(sourceFile)) {
        console.error(`❌ Source file ${sourceFile} not found`);
        process.exit(1);
    }
    
    // 确保 messages 目录存在
    if (!fs.existsSync("messages")) {
        fs.mkdirSync("messages", { recursive: true });
    }
    
    // 读取英文源文件
    let sourceContent;
    try {
        const sourceText = fs.readFileSync(sourceFile, 'utf-8');
        sourceContent = JSON.parse(sourceText);
        console.log(`✅ Successfully read source file: ${sourceFile}`);
    } catch (error) {
        console.error(`❌ Failed to read source file: ${error.message}`);
        process.exit(1);
    }
    
    // 统计信息
    const totalLanguages = Object.keys(LANGUAGES).length;
    let completed = 0;
    const failed = [];
    
    console.log(`📊 Target languages: ${totalLanguages}`);
    console.log("─".repeat(50));
    
    // 逐个语言进行翻译
    for (const [langCode, langName] of Object.entries(LANGUAGES)) {
        const targetFile = `messages/${langCode}.json`;
        
        // 检查文件是否已存在
        if (fs.existsSync(targetFile)) {
            console.log(`⏭️  Skipping ${langName} (${langCode}) - file exists`);
            completed++;
            continue;
        }
        
        try {
            // 翻译内容
            const translatedContent = await translateJsonContent(sourceContent, langCode, langName);
            
            if (translatedContent) {
                // 保存翻译结果
                fs.writeFileSync(targetFile, JSON.stringify(translatedContent, null, 2), 'utf-8');
                console.log(`✅ Success: ${langName} (${langCode}) -> ${targetFile}`);
                completed++;
            } else {
                console.log(`❌ Failed: ${langName} (${langCode}) - translation failed`);
                failed.push(`${langName} (${langCode})`);
            }
            
            // 添加延迟避免API限制
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.log(`❌ Failed: ${langName} (${langCode}) - ${error.message}`);
            failed.push(`${langName} (${langCode})`);
        }
        
        console.log("─".repeat(30));
    }
    
    // 输出统计结果
    console.log("\n" + "=".repeat(50));
    console.log("📈 Translation Summary:");
    console.log(`Total languages: ${totalLanguages}`);
    console.log(`Successfully translated: ${completed}`);
    console.log(`Failed: ${failed.length}`);
    
    if (failed.length > 0) {
        console.log("\n❌ Failed languages:");
        failed.forEach(lang => console.log(`  - ${lang}`));
        console.log("\n💡 You can re-run the script to retry failed translations");
    } else {
        console.log("\n🎉 All languages translated successfully!");
    }
    
    console.log("\n✨ Translation process completed!");
}

// 运行主函数
main().catch(error => {
    console.error("💥 Fatal error:", error);
    process.exit(1);
});
