#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版翻译脚本
"""

import requests
import json
import os
import time

# API 配置
url = "https://text.pollinations.ai/openai"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer D5iEptuNtJWrRFjC"
}

# 语言列表
languages = {
    "ar": "Arabic", "bg": "Bulgarian", "bn": "Bengali", "cs": "Czech", 
    "da": "Danish", "de": "German", "el": "Greek", "es": "Spanish",
    "et": "Estonian", "fi": "Finnish", "fr": "French", "he": "Hebrew",
    "hi": "Hindi", "hr": "Croatian", "hu": "Hungarian", "id": "Indonesian",
    "it": "Italian", "ja": "Japanese", "ko": "Korean", "lt": "Lithuanian",
    "lv": "Latvian", "ms": "Malay", "nl": "Dutch", "no": "Norwegian",
    "pl": "Polish", "pt": "Portuguese", "pt-BR": "Brazilian Portuguese",
    "ro": "Romanian", "ru": "Russian", "sk": "Slovak", "sl": "Slovenian",
    "sv": "Swedish", "th": "Thai", "tr": "Turkish", "uk": "Ukrainian",
    "vi": "Vietnamese", "zh-CN": "Simplified Chinese", "zh-TW": "Traditional Chinese"
}

def translate_to_language(source_json, target_lang, lang_name):
    """翻译到指定语言"""
    print(f"Translating to {lang_name}...")
    
    json_str = json.dumps(source_json, indent=2)
    
    payload = {
        "model": "openai-fast",
        "messages": [
            {
                "role": "system", 
                "content": f"You are a professional translator. Translate the JSON content to {lang_name}. Keep the JSON structure unchanged, only translate the string values, not the keys. Return valid JSON format."
            },
            {
                "role": "user", 
                "content": f"Translate this JSON to {lang_name}:\n\n{json_str}"
            }
        ],
        "seed": 101
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        response.raise_for_status()
        result = response.json()
        
        translated_text = result['choices'][0]['message']['content']
        
        # Clean up markdown formatting
        if "```json" in translated_text:
            translated_text = translated_text.split("```json")[1].split("```")[0].strip()
        elif "```" in translated_text:
            translated_text = translated_text.split("```")[1].strip()
        
        return json.loads(translated_text)
        
    except Exception as e:
        print(f"Error translating to {lang_name}: {e}")
        return None

def main():
    # Read source file
    with open("messages/en.json", "r", encoding="utf-8") as f:
        source_data = json.load(f)
    
    print(f"Starting translation for {len(languages)} languages...")
    
    for lang_code, lang_name in languages.items():
        output_file = f"messages/{lang_code}.json"
        
        # Skip if file exists
        if os.path.exists(output_file):
            print(f"Skipping {lang_name} - file exists")
            continue
        
        # Translate
        translated = translate_to_language(source_data, lang_code, lang_name)
        
        if translated:
            # Save result
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(translated, f, ensure_ascii=False, indent=2)
            print(f"✅ Saved: {output_file}")
        else:
            print(f"❌ Failed: {lang_name}")
        
        # Wait to avoid rate limits
        time.sleep(2)
    
    print("Translation complete!")

if __name__ == "__main__":
    main()
