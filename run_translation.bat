@echo off
echo Starting translation process...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Install required packages
echo Installing required packages...
pip install requests

REM Run the translation script
echo.
echo Running translation script...
python translate_messages.py

echo.
echo Translation process completed!
pause
