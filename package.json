{"name": "mygame", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "parse-excel": "bun run scripts/parseExcel.ts", "download-images": "bun run scripts/downloadImages.ts", "translate": "bun run scripts/translate.ts", "translate-markdown": "bun run scripts/translateMarkdown.ts", "add-game": "bun run scripts/parseExcel.ts", "add-game-legacy": "bun run scripts/addGame.ts", "delete-games": "bun run scripts/deleteGames.ts", "create-messages": "bun run scripts/createMessageFiles.ts", "create-descriptions": "bun run scripts/createGameDescriptions.ts", "enhance-descriptions": "bun run scripts/enhanceGameDescription.ts", "retry-descriptions": "bun run scripts/enhanceGameDescription.ts retry", "priority": "bun run scripts/gamePriority.ts", "game:stats": "bun run scripts/gameManager.ts stats", "game:list": "bun run scripts/gameManager.ts list", "game:backup": "bun run scripts/gameManager.ts backup", "game:clean": "bun run scripts/gameManager.ts clean", "game:delete": "bun run scripts/gameManager.ts delete", "game:manage": "bun run scripts/gameManager.ts", "games:add-excel": "bun run scripts/gameManager.ts add-excel", "games:add-folder": "bun run scripts/gameManager.ts add-folder", "games:add-json": "bun run scripts/gameManager.ts add-json", "games:download": "bun run scripts/gameManager.ts download-images", "games:translate": "bun run scripts/gameManager.ts translate", "translate-messages": "bun run translate.js"}, "dependencies": {"@types/xlsx": "^0.0.36", "axios": "^1.11.0", "next": "15.5.0", "next-intl": "^4.3.5", "react": "19.1.0", "react-dom": "19.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.5.0", "@eslint/eslintrc": "^3"}}