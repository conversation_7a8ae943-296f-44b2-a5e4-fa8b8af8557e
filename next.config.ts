import createNextIntlPlugin from 'next-intl/plugin';
import type { NextConfig } from 'next';

const withNextIntl = createNextIntlPlugin('./i18n/request.ts');

const nextConfig: NextConfig = {
  images: {
    domains: [
      'images.crazygames.com',
      'img.y8.com',
      'localhost'
    ],
    remotePatterns: [
      {
        protocol: 'https' as const,
        hostname: '**',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'http' as const,
        hostname: '**',
        port: '',
        pathname: '**',
      }
    ]
  },
  env: {
    SITE_DOMAIN: process.env.SITE_DOMAIN || 'ai-game.site'
  }
};

export default withNextIntl(nextConfig);
