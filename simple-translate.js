#!/usr/bin/env bun
/**
 * 简化版翻译脚本
 * 运行命令: bun run simple-translate.js
 */

import fs from 'fs';

// API 配置
const url = "https://text.pollinations.ai/openai";
const headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer D5iEptuNtJWrRFjC"
};

// 语言列表
const languages = {
    "ar": "Arabic", "bg": "Bulgarian", "bn": "Bengali", "cs": "Czech", 
    "da": "Danish", "de": "German", "el": "Greek", "es": "Spanish",
    "et": "Estonian", "fi": "Finnish", "fr": "French", "he": "Hebrew",
    "hi": "Hindi", "hr": "Croatian", "hu": "Hungarian", "id": "Indonesian",
    "it": "Italian", "ja": "Japanese", "ko": "Korean", "lt": "Lithuanian",
    "lv": "Latvian", "ms": "Malay", "nl": "Dutch", "no": "Norwegian",
    "pl": "Polish", "pt": "Portuguese", "pt-BR": "Brazilian Portuguese",
    "ro": "Romanian", "ru": "Russian", "sk": "Slovak", "sl": "Slovenian",
    "sv": "Swedish", "th": "Thai", "tr": "Turkish", "uk": "Ukrainian",
    "vi": "Vietnamese", "zh-CN": "Simplified Chinese", "zh-TW": "Traditional Chinese"
};

async function translateToLanguage(sourceJson, targetLang, langName) {
    console.log(`🔄 Translating to ${langName}...`);
    
    const jsonStr = JSON.stringify(sourceJson, null, 2);
    
    const payload = {
        model: "openai-fast",
        messages: [
            {
                role: "system", 
                content: `You are a professional translator. Translate the JSON content to ${langName}. Keep the JSON structure unchanged, only translate the string values, not the keys. Return valid JSON format.`
            },
            {
                role: "user", 
                content: `Translate this JSON to ${langName}:\n\n${jsonStr}`
            }
        ],
        seed: 101
    };
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(payload)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const result = await response.json();
        let translatedText = result.choices[0].message.content;
        
        // Clean up markdown formatting
        if (translatedText.includes("```json")) {
            translatedText = translatedText.split("```json")[1].split("```")[0].trim();
        } else if (translatedText.includes("```")) {
            translatedText = translatedText.split("```")[1].trim();
        }
        
        return JSON.parse(translatedText);
        
    } catch (error) {
        console.error(`❌ Error translating to ${langName}: ${error.message}`);
        return null;
    }
}

async function main() {
    console.log("🚀 Starting translation...");
    
    // Read source file
    const sourceData = JSON.parse(fs.readFileSync("messages/en.json", "utf-8"));
    
    console.log(`📊 Translating to ${Object.keys(languages).length} languages...`);
    
    for (const [langCode, langName] of Object.entries(languages)) {
        const outputFile = `messages/${langCode}.json`;
        
        // Skip if file exists
        if (fs.existsSync(outputFile)) {
            console.log(`⏭️  Skipping ${langName} - file exists`);
            continue;
        }
        
        // Translate
        const translated = await translateToLanguage(sourceData, langCode, langName);
        
        if (translated) {
            // Save result
            fs.writeFileSync(outputFile, JSON.stringify(translated, null, 2), "utf-8");
            console.log(`✅ Saved: ${outputFile}`);
        } else {
            console.log(`❌ Failed: ${langName}`);
        }
        
        // Wait to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log("🎉 Translation complete!");
}

main().catch(console.error);
