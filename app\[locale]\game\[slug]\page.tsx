import { getGameBySlugServer, getRecommendedGamesServer, getGameMessages } from '@/lib/gameData';
import { notFound } from 'next/navigation';
import SafeGamePlayer from '@/components/SafeGamePlayer';
import RecommendedGames from '@/components/RecommendedGames';
import { getTranslations } from 'next-intl/server';
import GameBackground from '@/components/GameBackground';
import GameHeader from '@/components/GameHeader';

export const runtime = 'edge';

interface GamePageProps {
  params: Promise<{ locale: string; slug: string }>;
}

export async function generateMetadata({ params }: GamePageProps) {
  const { locale, slug } = await params;
  const game = getGameBySlugServer(slug);
  
  if (!game) {
    return {
      title: 'Game Not Found',
    };
  }

  const gameMessages = await getGameMessages(slug, locale);
  const title = gameMessages.title || game.title;
  const description = gameMessages.description || game.description;

  return {
    title: `${title} - AI Game Site`,
    description: description,
    keywords: `${title}, free game, online game, ${game.category}`,
    openGraph: {
      title: `${title} - AI Game Site`,
      description: description,
      type: 'website',
      locale: locale,
      images: [{
        url: game.localCoverUrl || game.coverUrl,
        width: 800,
        height: 600,
        alt: title
      }]
    },
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/${locale}/game/${slug}`
    }
  };
}

export default async function GamePage({ params }: GamePageProps) {
  const { locale, slug } = await params;
  const game = getGameBySlugServer(slug);

  if (!game) {
    notFound();
  }

  const t = await getTranslations({ locale, namespace: 'game' });
  const gameMessages = await getGameMessages(slug, locale);
  const recommendedGames = getRecommendedGamesServer(game, 6);

  return (
    <div className="min-h-screen relative">
      {/* 动态背景 */}
      <GameBackground />

      {/* 游戏站Header */}
      <GameHeader />

      <div className="container mx-auto px-4 pt-24 pb-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 主游戏区域 */}
          <div className="lg:col-span-3">
            <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 shadow-2xl overflow-hidden">
              {/* 游戏标题 */}
              <div className="p-6 border-b border-white/20">
                <h1 className="text-3xl font-bold text-white mb-2">
                  {gameMessages.title || game.title}
                </h1>
                <div className="flex items-center space-x-4 text-sm text-gray-300">
                  <span className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-3 py-1 rounded-full">
                    {gameMessages.category || game.category}
                  </span>
                  <span>{new Date(game.publishDate).toLocaleDateString(locale)}</span>
                </div>
              </div>

              {/* 游戏播放器 */}
              <SafeGamePlayer game={game} />

              {/* 游戏信息 */}
              <div className="p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-white">{t('description')}</h3>
                    <p className="text-gray-300 leading-relaxed">
                      {gameMessages.description || game.description}
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2 text-white">{t('instructions')}</h4>
                      <p className="text-gray-400 text-sm">
                        {gameMessages.instructions || 'Use arrow keys to move, space to jump.'}
                      </p>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2 text-white">{t('controls')}</h4>
                      <p className="text-gray-400 text-sm">
                        {gameMessages.controls || 'Mouse and keyboard controls supported.'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 侧边栏推荐游戏 */}
          <div className="lg:col-span-1">
            <RecommendedGames games={recommendedGames} />
          </div>
        </div>
      </div>
    </div>
  );
}