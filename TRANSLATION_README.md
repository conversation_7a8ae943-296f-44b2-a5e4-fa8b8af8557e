# 🌍 自动翻译脚本使用指南

## 📋 功能说明

这些JavaScript脚本可以自动将 `messages/en.json` 翻译为39种语言，使用Bun运行时执行。

## 🚀 快速开始

### 方法一：使用npm脚本（推荐）
```bash
bun run translate-messages
```

### 方法二：直接运行脚本
```bash
# 完整版本（推荐）
bun run translate.js

# 简化版本
bun run simple-translate.js
```

## 🌍 支持的39种语言

- 🇸🇦 阿拉伯语 (ar)
- 🇧🇬 保加利亚语 (bg) 
- 🇧🇩 孟加拉语 (bn)
- 🇨🇿 捷克语 (cs)
- 🇩🇰 丹麦语 (da)
- 🇩🇪 德语 (de)
- 🇬🇷 希腊语 (el)
- 🇪🇸 西班牙语 (es)
- 🇪🇪 爱沙尼亚语 (et)
- 🇫🇮 芬兰语 (fi)
- 🇫🇷 法语 (fr)
- 🇮🇱 希伯来语 (he)
- 🇮🇳 印地语 (hi)
- 🇭🇷 克罗地亚语 (hr)
- 🇭🇺 匈牙利语 (hu)
- 🇮🇩 印尼语 (id)
- 🇮🇹 意大利语 (it)
- 🇯🇵 日语 (ja)
- 🇰🇷 韩语 (ko)
- 🇱🇹 立陶宛语 (lt)
- 🇱🇻 拉脱维亚语 (lv)
- 🇲🇾 马来语 (ms)
- 🇳🇱 荷兰语 (nl)
- 🇳🇴 挪威语 (no)
- 🇵🇱 波兰语 (pl)
- 🇵🇹 葡萄牙语 (pt)
- 🇧🇷 巴西葡萄牙语 (pt-BR)
- 🇷🇴 罗马尼亚语 (ro)
- 🇷🇺 俄语 (ru)
- 🇸🇰 斯洛伐克语 (sk)
- 🇸🇮 斯洛文尼亚语 (sl)
- 🇸🇪 瑞典语 (sv)
- 🇹🇭 泰语 (th)
- 🇹🇷 土耳其语 (tr)
- 🇺🇦 乌克兰语 (uk)
- 🇻🇳 越南语 (vi)
- 🇨🇳 中文简体 (zh-CN)
- 🇹🇼 中文繁体 (zh-TW)

## 📁 文件说明

- `translate.js` - 主翻译脚本（功能完整，推荐使用）
- `simple-translate.js` - 简化版翻译脚本
- `TRANSLATION_README.md` - 本使用说明

## ⚙️ 脚本特性

### 主脚本 (translate.js)
- ✅ 完整的错误处理和重试机制
- ✅ 详细的进度显示和统计
- ✅ 智能跳过已存在的文件
- ✅ 专业的翻译提示词
- ✅ 自动清理API响应格式

### 简化脚本 (simple-translate.js)
- ✅ 代码简洁易懂
- ✅ 快速执行
- ✅ 适合测试和调试

## 🔧 配置说明

脚本使用 Pollinations AI API：
- **API地址**: `https://text.pollinations.ai/openai`
- **模型**: `openai-fast`
- **认证**: Bearer Token已配置

## 📝 使用注意事项

1. **前置要求**
   - 确保已安装 Bun 运行时
   - 确保 `messages/en.json` 文件存在

2. **网络要求**
   - 需要稳定的网络连接
   - 确认可以访问API地址

3. **时间消耗**
   - 翻译39种语言大约需要5-15分钟
   - 每个语言之间有1-2秒延迟避免API限制

4. **文件处理**
   - 脚本会自动跳过已存在的翻译文件
   - 如需重新翻译，请先删除对应的语言文件

## 🎯 输出结果

翻译完成后，`messages/` 目录下会生成：
```
messages/
├── en.json          # 英文原文
├── ar.json          # 阿拉伯语
├── bg.json          # 保加利亚语
├── bn.json          # 孟加拉语
├── ...              # 其他36种语言
└── zh-TW.json       # 中文繁体
```

## 🔍 质量保证

1. **JSON结构保持**: 只翻译字符串值，保持键名不变
2. **游戏术语**: 使用常见的游戏行业术语
3. **自然表达**: 符合目标语言的表达习惯
4. **格式清理**: 自动处理API返回的markdown格式

## 🛠️ 故障排除

### 常见问题

1. **Bun未安装**
   ```bash
   # 安装Bun
   curl -fsSL https://bun.sh/install | bash
   ```

2. **网络连接问题**
   - 检查网络连接
   - 确认API地址可访问

3. **翻译失败**
   - 查看控制台错误信息
   - 尝试重新运行脚本
   - 使用简化版本测试

4. **JSON格式错误**
   - 脚本会自动处理大部分格式问题
   - 如有问题可以手动编辑修复

### 重新翻译特定语言

```bash
# 删除特定语言文件
rm messages/fr.json

# 重新运行翻译
bun run translate-messages
```

## 📊 执行示例

```bash
$ bun run translate-messages

🚀 Starting translation process...

✅ Successfully read source file: messages/en.json
📊 Target languages: 39
──────────────────────────────────────────────────
🔄 Translating to Arabic (ar)...
✅ Success: Arabic (ar) -> messages/ar.json
──────────────────────────────
🔄 Translating to Bulgarian (bg)...
✅ Success: Bulgarian (bg) -> messages/bg.json
──────────────────────────────
...

==================================================
📈 Translation Summary:
Total languages: 39
Successfully translated: 39
Failed: 0

🎉 All languages translated successfully!

✨ Translation process completed!
```

## 💡 使用建议

1. **首次运行**: 建议先测试几种主要语言
2. **质量检查**: 翻译完成后检查重要内容的准确性
3. **备份原文**: 始终保留 `en.json` 作为参考
4. **定期更新**: 当英文内容更新时重新翻译
5. **人工校对**: 对于重要的游戏术语建议人工校对

## 🔗 相关命令

```bash
# 查看所有可用脚本
bun run

# 运行翻译（推荐）
bun run translate-messages

# 直接运行脚本
bun run translate.js
bun run simple-translate.js
```
