import { GameData, CategoryData, GameMessages } from './types';
import * as fs from 'fs';
import * as path from 'path';

// 缓存变量
let gamesCache: GameData[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

// 服务器端数据获取函数 - 带缓存优化
export function getAllGamesServer(): GameData[] {
  try {
    const now = Date.now();

    // 检查缓存是否有效
    if (gamesCache && (now - cacheTimestamp) < CACHE_DURATION) {
      return gamesCache;
    }

    const gamesPath = path.join(process.cwd(), 'data', 'games.json');
    if (fs.existsSync(gamesPath)) {
      const games: GameData[] = JSON.parse(fs.readFileSync(gamesPath, 'utf-8'));

      // 按优先级排序，优先级高的排在前面
      const sortedGames = games.sort((a, b) => {
        const priorityA = a.priority || 0;
        const priorityB = b.priority || 0;

        // 优先级不同时按优先级排序
        if (priorityB !== priorityA) {
          return priorityB - priorityA;
        }

        // 优先级相同时按发布日期排序
        return new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime();
      });

      // 更新缓存
      gamesCache = sortedGames;
      cacheTimestamp = now;

      return sortedGames;
    }
  } catch (error) {
    console.error('读取游戏数据失败:', error);
  }
  return [];
}

/**
 * 获取首页游戏列表 - 优化版本，只返回首页需要的游戏
 */
export function getHomePageGamesServer(limit: number = 50): GameData[] {
  const games = getAllGamesServer();

  // 获取高优先级游戏（至少10个）
  const highPriorityGames = games.filter(game => (game.priority || 0) >= 7);
  const highPriorityCount = Math.min(highPriorityGames.length, Math.max(10, Math.floor(limit * 0.3)));

  // 获取普通游戏
  const normalGames = games.filter(game => (game.priority || 0) < 7);
  const normalCount = limit - highPriorityCount;

  // 合并并返回
  return [
    ...highPriorityGames.slice(0, highPriorityCount),
    ...normalGames.slice(0, normalCount)
  ];
}

/**
 * 获取高优先级游戏列表
 */
export function getHighPriorityGamesServer(minPriority: number = 7): GameData[] {
  const games = getAllGamesServer();
  return games.filter(game => (game.priority || 0) >= minPriority);
}

/**
 * 获取普通优先级游戏列表
 */
export function getNormalPriorityGamesServer(): GameData[] {
  const games = getAllGamesServer();
  return games.filter(game => (game.priority || 0) < 7);
}

/**
 * 获取分页游戏列表
 */
export function getPaginatedGamesServer(page: number = 1, pageSize: number = 24): {
  games: GameData[];
  totalPages: number;
  totalGames: number;
  currentPage: number;
} {
  const allGames = getAllGamesServer();
  const totalGames = allGames.length;
  const totalPages = Math.ceil(totalGames / pageSize);
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  return {
    games: allGames.slice(startIndex, endIndex),
    totalPages,
    totalGames,
    currentPage: page
  };
}

export function getCategoriesDataServer(): CategoryData {
  try {
    const categoriesPath = path.join(process.cwd(), 'data', 'categories.json');
    if (fs.existsSync(categoriesPath)) {
      return JSON.parse(fs.readFileSync(categoriesPath, 'utf-8'));
    }
  } catch (error) {
    console.error('读取分类数据失败:', error);
  }
  return {};
}

export function getGameBySlugServer(slug: string): GameData | null {
  const games = getAllGamesServer();
  return games.find(game => game.slug === slug) || null;
}

export function getGamesByCategoryServer(category: string): GameData[] {
  const categoriesData = getCategoriesDataServer();
  return categoriesData[category] || [];
}

export function getRecommendedGamesServer(currentGame: GameData, limit: number = 6): GameData[] {
  const categoryGames = getGamesByCategoryServer(currentGame.category);
  return categoryGames
    .filter(game => game.id !== currentGame.id)
    .slice(0, limit);
}

export function searchGamesServer(query: string): GameData[] {
  const games = getAllGamesServer();
  const lowercaseQuery = query.toLowerCase();
  
  return games.filter(game => 
    game.title.toLowerCase().includes(lowercaseQuery) ||
    game.description.toLowerCase().includes(lowercaseQuery) ||
    game.category.toLowerCase().includes(lowercaseQuery)
  );
}

export function getAllCategoriesServer(): string[] {
  const categoriesData = getCategoriesDataServer();
  return Object.keys(categoriesData);
}

/**
 * 获取游戏的详细介绍内容（支持多语言Markdown格式）
 */
export function getGameDescriptionContent(gameSlug: string, locale: string = 'en'): string {
  try {
    // 尝试获取指定语言的介绍文件
    const descriptionPath = path.join(process.cwd(), 'data', 'games', gameSlug, `description.${locale}.md`);
    
    if (fs.existsSync(descriptionPath)) {
      return fs.readFileSync(descriptionPath, 'utf-8');
    }
    
    // 如果指定语言不存在，尝试英文版本
    if (locale !== 'en') {
      const englishPath = path.join(process.cwd(), 'data', 'games', gameSlug, 'description.en.md');
      if (fs.existsSync(englishPath)) {
        return fs.readFileSync(englishPath, 'utf-8');
      }
    }
    
    // 兼容旧版本：尝试读取无语言后缀的文件
    const legacyPath = path.join(process.cwd(), 'data', 'games', gameSlug, 'description.md');
    if (fs.existsSync(legacyPath)) {
      return fs.readFileSync(legacyPath, 'utf-8');
    }
    
    // 如果没有单独的介绍文件，从games.json中获取
    const game = getGameBySlugServer(gameSlug);
    if (game && game.description) {
      return game.description;
    }
    
    return locale === 'en' ? 'No game description available' : '暂无游戏介绍';
  } catch (error) {
    console.error(`获取游戏介绍失败 ${gameSlug}:`, error);
    return locale === 'en' ? 'Error loading game description' : '获取游戏介绍时出现错误';
  }
}

/**
 * 获取游戏的简短描述（用于卡片展示等）
 */
export function getGameShortDescription(gameSlug: string, locale: string = 'en'): string {
  try {
    // 优先从单独的介绍文件中提取
    const fullDescription = getGameDescriptionContent(gameSlug, locale);
    
    // 如果是Markdown格式，提取第一段文本
    const introHeaders = ['## Game Introduction', '## 游戏简介', '## Introducción del Juego', '## ゲーム紹介'];
    
    for (const header of introHeaders) {
      if (fullDescription.includes(header)) {
        const sections = fullDescription.split('##');
        const introSection = sections.find(section => 
          section.trim().startsWith(header.replace('## ', ''))
        );
        if (introSection) {
          const content = introSection.replace(/^[^#\n]*/, '').trim();
          const firstParagraph = content.split('\n').find(line => line.trim().length > 0);
          if (firstParagraph) {
            return firstParagraph.trim();
          }
        }
      }
    }
    
    // 如果不是Markdown格式，或者找不到介绍部分，直接返回前200个字符
    const cleanText = fullDescription.replace(/^#.*$/gm, '').replace(/^\s*$/gm, '').trim();
    return cleanText.length > 200 
      ? cleanText.substring(0, 200) + '...'
      : cleanText || (locale === 'en' ? 'This is an exciting game!' : '这是一个精彩的游戏！');
  } catch (error) {
    console.error(`获取游戏简短介绍失败 ${gameSlug}:`, error);
    return locale === 'en' ? 'No game description available' : '暂无游戏介绍';
  }
}

export async function getGameMessages(gameSlug: string, locale: string): Promise<GameMessages> {
  try {
    // 优先从Markdown文件获取内容
    const markdownContent = getGameDescriptionContent(gameSlug, locale);
    
    if (markdownContent && markdownContent !== 'No game description available' && markdownContent !== '暂无游戏介绍') {
      // 从Markdown解析游戏信息
      const parsedInfo = parseMarkdownGameInfo(markdownContent, gameSlug, locale);
      if (parsedInfo.title || parsedInfo.description) {
        return parsedInfo;
      }
    }
    
    // 如果Markdown文件不存在或解析失败，回退到JSON文件（兼容性）
    const messagesPath = path.join(
      process.cwd(), 
      'data', 
      'games', 
      gameSlug, 
      'messages', 
      `${locale}.json`
    );
    
    if (fs.existsSync(messagesPath)) {
      const messages = JSON.parse(fs.readFileSync(messagesPath, 'utf-8'));
      return messages;
    }
    
    // 回退到英文JSON
    if (locale !== 'en') {
      const enPath = path.join(
        process.cwd(), 
        'data', 
        'games', 
        gameSlug, 
        'messages', 
        'en.json'
      );
      if (fs.existsSync(enPath)) {
        const messages = JSON.parse(fs.readFileSync(enPath, 'utf-8'));
        return messages;
      }
    }
  } catch (error) {
    console.error(`获取游戏消息失败 ${gameSlug}:`, error);
  }
  
  // 最后的回退：从games.json获取基本信息
  const game = getGameBySlugServer(gameSlug);
  const defaultDescription = getGameShortDescription(gameSlug, locale);
  
  return {
    title: game?.title || '',
    description: defaultDescription,
    category: game?.category || '',
    instructions: locale === 'en' ? 'Use arrow keys to move, space to jump.' : '使用方向键移动，空格键跳跃。',
    controls: locale === 'en' ? 'Mouse and keyboard controls supported.' : '支持鼠标和键盘控制。'
  };
}

/**
 * 从Markdown内容解析游戏信息
 */
function parseMarkdownGameInfo(markdownContent: string, gameSlug: string, locale: string): GameMessages {
  try {
    const lines = markdownContent.split('\n');
    let title = '';
    let description = '';
    let category = '';
    let instructions = '';
    let controls = '';
    
    // 解析标题
    const titleLine = lines.find(line => line.startsWith('# '));
    if (titleLine) {
      title = titleLine.replace('# ', '').trim();
    }
    
    // 解析各个部分
    let currentSection = '';
    let currentContent: string[] = [];
    
    for (const line of lines) {
      if (line.startsWith('## ')) {
        // 保存上一个部分的内容
        if (currentSection && currentContent.length > 0) {
          const content = currentContent.join('\n').trim();
          switch (currentSection.toLowerCase()) {
            case 'game introduction':
            case '游戏简介':
            case 'introducción del juego':
            case 'ゲーム紹介':
              description = content;
              break;
            case 'controls':
            case '操作说明':
            case 'controles':
            case '操作方法':
              controls = content;
              break;
            case 'game tips':
            case '游戏提示':
            case 'consejos del juego':
            case 'ゲームのヒント':
              instructions = content;
              break;
            case 'category information':
            case '分类信息':
            case 'información de categoría':
            case 'カテゴリー情報':
              // 从分类信息中提取主要分类
              const categoryMatch = content.match(/(?:Main Category|主要分类|Categoría Principal|メインカテゴリー):\s*(.+)/);
              if (categoryMatch) {
                category = categoryMatch[1].trim();
              }
              break;
          }
        }
        
        // 开始新的部分
        currentSection = line.replace('## ', '').trim();
        currentContent = [];
      } else if (currentSection && line.trim()) {
        currentContent.push(line);
      }
    }
    
    // 处理最后一个部分
    if (currentSection && currentContent.length > 0) {
      const content = currentContent.join('\n').trim();
      switch (currentSection.toLowerCase()) {
        case 'category information':
        case '分类信息':
        case 'información de categoría':
        case 'カテゴリー情報':
          const categoryMatch = content.match(/(?:Main Category|主要分类|Categoría Principal|メインカテゴリー):\s*(.+)/);
          if (categoryMatch) {
            category = categoryMatch[1].trim();
          }
          break;
      }
    }
    
    return {
      title: title || '',
      description: description || getGameShortDescription(gameSlug, locale),
      category: category || '',
      instructions: instructions || (locale === 'en' ? 'Use arrow keys to move, space to jump.' : '使用方向键移动，空格键跳跃。'),
      controls: controls || (locale === 'en' ? 'Mouse and keyboard controls supported.' : '支持鼠标和键盘控制。')
    };
  } catch (error) {
    console.error(`解析Markdown游戏信息失败 ${gameSlug}:`, error);
    return {
      title: '',
      description: locale === 'en' ? 'No game description available' : '暂无游戏介绍',
      category: '',
      instructions: locale === 'en' ? 'Use arrow keys to move, space to jump.' : '使用方向键移动，空格键跳跃。',
      controls: locale === 'en' ? 'Mouse and keyboard controls supported.' : '支持鼠标和键盘控制。'
    };
  }
}