export interface GameData {
  id: string;
  title: string;
  coverUrl: string;
  description: string;
  category: string; // 主要分类
  allCategories?: string[]; // 所有分类（新增）
  iframeUrl: string;
  pageUrl: string;
  publishDate: string;
  slug: string;
  localCoverUrl?: string;
  priority?: number; // 游戏优先级（1-10，数字越大优先级越高）
}

export interface GameMessages {
  title: string;
  description: string;
  category: string;
  instructions: string;
  controls: string;
}

export interface CategoryData {
  [category: string]: GameData[];
}

export interface CategoryInfo {
  id: string;
  name: string;
  count: number;
}

export type GameSize = '1x1' | '2x2' | '3x3';

export interface GameTileProps {
  game: GameData;
  size: GameSize;
  locale: string;
}