import { searchGamesServer } from '@/lib/gameData';
import MasonryGameGrid from '@/components/MasonryGameGrid';

export const runtime = 'edge';

interface SearchPageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ q?: string }>;
}

export async function generateMetadata({ searchParams }: SearchPageProps) {
  const { q } = await searchParams;
  
  const title = q ? `Search: ${q} - AI Game Site` : 'Search Games - AI Game Site';
  
  return {
    title,
    description: q ? `Search results for &quot;${q}&quot;` : 'Search for your favorite games',
    robots: 'noindex, follow'
  };
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  const { q } = await searchParams;
  const query = q || '';
  
  const searchResults = query ? searchGamesServer(query) : [];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 搜索结果标题 */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Search Results
          </h1>
          {query && (
            <p className="text-gray-600">
              {searchResults.length} results found for &quot;{query}&quot;
            </p>
          )}
        </div>

        {/* 搜索结果 */}
        {query ? (
          searchResults.length > 0 ? (
            <MasonryGameGrid games={searchResults} />
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🔍</div>
              <h2 className="text-2xl font-bold text-gray-600 mb-4">
                No games found
              </h2>
              <p className="text-gray-500 mb-6">
                We couldn&apos;t find any games matching &quot;{query}&quot;. Try searching with different keywords.
              </p>
              <div className="text-sm text-gray-400">
                <p>Tips:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Check your spelling</li>
                  <li>Try shorter or more general terms</li>
                  <li>Try searching by game category</li>
                </ul>
              </div>
            </div>
          )
        ) : (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🎮</div>
            <h2 className="text-2xl font-bold text-gray-600 mb-4">
              Search for Games
            </h2>
            <p className="text-gray-500">
              Enter a game name, category, or keyword in the search box above to find your favorite games.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}