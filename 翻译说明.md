# 自动翻译脚本使用说明

## 📋 功能说明

这个脚本可以自动将 `messages/en.json` 翻译为39种语言，生成对应的翻译文件。

## 🌍 支持的语言

包含以下39种语言：
- 阿拉伯语 (ar)
- 保加利亚语 (bg) 
- 孟加拉语 (bn)
- 捷克语 (cs)
- 丹麦语 (da)
- 德语 (de)
- 希腊语 (el)
- 西班牙语 (es)
- 爱沙尼亚语 (et)
- 芬兰语 (fi)
- 法语 (fr)
- 希伯来语 (he)
- 印地语 (hi)
- 克罗地亚语 (hr)
- 匈牙利语 (hu)
- 印尼语 (id)
- 意大利语 (it)
- 日语 (ja)
- 韩语 (ko)
- 立陶宛语 (lt)
- 拉脱维亚语 (lv)
- 马来语 (ms)
- 荷兰语 (nl)
- 挪威语 (no)
- 波兰语 (pl)
- 葡萄牙语 (pt)
- 巴西葡萄牙语 (pt-BR)
- 罗马尼亚语 (ro)
- 俄语 (ru)
- 斯洛伐克语 (sk)
- 斯洛文尼亚语 (sl)
- 瑞典语 (sv)
- 泰语 (th)
- 土耳其语 (tr)
- 乌克兰语 (uk)
- 越南语 (vi)
- 中文简体 (zh-CN)
- 中文繁体 (zh-TW)

## 🚀 使用方法

### 方法一：使用批处理文件（推荐）
1. 双击运行 `run_translation.bat`
2. 脚本会自动安装依赖并开始翻译

### 方法二：手动运行
1. 确保已安装 Python 3.6+
2. 安装依赖：
   ```bash
   pip install requests
   ```
3. 运行翻译脚本：
   ```bash
   python translate_messages.py
   ```

### 方法三：使用简化版本
如果主脚本有问题，可以使用简化版本：
```bash
python simple_translate.py
```

## 📁 文件说明

- `translate_messages.py` - 主翻译脚本（功能完整）
- `simple_translate.py` - 简化版翻译脚本
- `run_translation.bat` - Windows批处理文件
- `翻译说明.md` - 本说明文件

## ⚙️ 配置说明

脚本使用 Pollinations AI API，配置信息：
- API地址：`https://text.pollinations.ai/openai`
- 模型：`openai-fast`
- 认证：已配置Bearer Token

## 📝 注意事项

1. **网络要求**：需要稳定的网络连接
2. **时间消耗**：翻译39种语言大约需要5-10分钟
3. **文件覆盖**：脚本会跳过已存在的翻译文件
4. **错误处理**：如果某个语言翻译失败，会继续翻译其他语言
5. **API限制**：脚本已添加延迟避免API限制

## 🔧 故障排除

### 常见问题

1. **Python未安装**
   - 下载并安装 Python 3.6+
   - 确保添加到系统PATH

2. **网络连接问题**
   - 检查网络连接
   - 确认可以访问API地址

3. **翻译质量问题**
   - 可以手动编辑生成的翻译文件
   - 重要术语建议人工校对

4. **JSON格式错误**
   - 脚本会自动处理大部分格式问题
   - 如有问题可以重新运行

### 重新翻译特定语言

如果需要重新翻译某个语言：
1. 删除对应的 `.json` 文件
2. 重新运行脚本

## 📊 输出结果

翻译完成后，`messages/` 目录下会生成：
- `ar.json` - 阿拉伯语
- `bg.json` - 保加利亚语
- `bn.json` - 孟加拉语
- ... (其他37种语言)

每个文件都保持与 `en.json` 相同的JSON结构，只翻译了字符串值。

## 🎯 使用建议

1. **首次运行**：建议先测试几种语言
2. **质量检查**：翻译完成后检查重要内容
3. **备份原文**：保留 `en.json` 作为参考
4. **定期更新**：当英文内容更新时重新翻译

## 📞 技术支持

如果遇到问题：
1. 检查网络连接
2. 确认Python环境
3. 查看控制台错误信息
4. 尝试使用简化版脚本
