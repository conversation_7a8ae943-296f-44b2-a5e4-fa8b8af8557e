import { Metadata } from 'next';
import SafeGamePlayer from '@/components/SafeGamePlayer';
import GamePlayer from '@/components/GamePlayer';
import { GameData } from '@/lib/types';

export const runtime = 'edge';

export const metadata: Metadata = {
  title: 'iframe测试页面',
  description: '测试iframe嵌入游戏的解决方案',
};

export default function TestIframePage() {
  // 测试游戏数据
  const testGame: GameData = {
    id: 'test-fnf-garcello',
    title: 'FNF Garcello (测试)',
    coverUrl: 'https://www.onlinegames.io/media/posts/914/fnf-garcello.jpg',
    description: '测试iframe嵌入的FNF Garcello游戏',
    category: 'Music',
    iframeUrl: 'https://cloud.onlinegames.io/games/2024/cube/fnf-garcello/index.html',
    pageUrl: 'https://www.onlinegames.io/fnf-garcello/',
    publishDate: '2025-01-28',
    slug: 'test-fnf-garcello',
    localCoverUrl: '/images/games/fnf-garcello-223.jpg'
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            {/* 页面标题 */}
            <div className="p-6 border-b border-gray-200">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                iframe嵌入测试页面
              </h1>
              <p className="text-gray-600">
                测试不同的iframe嵌入解决方案，解决onlinegames.io等网站的iframe限制问题
              </p>
            </div>

            {/* 游戏播放器 */}
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">安全游戏播放器</h2>
              <SafeGamePlayer game={testGame} />
            </div>

            {/* 对比播放器 */}
            <div className="p-6 border-t border-gray-200">
              <h2 className="text-xl font-semibold mb-4">基础播放器对比</h2>

              <div>
                <h3 className="text-lg font-medium mb-3">基础游戏播放器</h3>
                <GamePlayer game={testGame} />
              </div>
            </div>

            {/* 说明信息 */}
            <div className="p-6 bg-gray-50">
              <h3 className="text-lg font-semibold mb-3">安全iframe解决方案</h3>
              <div className="space-y-3 text-sm text-gray-700">
                <div className="bg-white p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-600 mb-2">1. 智能错误检测</h4>
                  <p>自动识别iframe限制和网络问题，提供相应的错误信息</p>
                </div>

                <div className="bg-white p-4 rounded-lg">
                  <h4 className="font-semibold text-green-600 mb-2">2. 用户留存优先</h4>
                  <p>不跳转到外部网站，保持用户在本站内的体验</p>
                </div>

                <div className="bg-white p-4 rounded-lg">
                  <h4 className="font-semibold text-purple-600 mb-2">3. 友好的错误处理</h4>
                  <p>提供清晰的错误说明和重试机制，最多允许3次重试</p>
                </div>
              </div>
            </div>

            {/* 技术细节 */}
            <div className="p-6 border-t border-gray-200">
              <h3 className="text-lg font-semibold mb-3">技术实现</h3>
              <div className="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-semibold mb-2">安全特性:</h4>
                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                    <li>不使用代理服务器</li>
                    <li>不跳转到外部网站</li>
                    <li>保持用户在本站内</li>
                    <li>安全的iframe沙箱设置</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">用户体验特性:</h4>
                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                    <li>智能错误类型识别</li>
                    <li>15秒加载超时检测</li>
                    <li>最多3次重试机制</li>
                    <li>清晰的错误说明</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* 测试信息 */}
            <div className="p-6 bg-blue-50">
              <h3 className="text-lg font-semibold mb-3">测试信息</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">测试游戏: </span>
                  <span className="text-gray-700">{testGame.title}</span>
                </div>
                <div>
                  <span className="font-medium">游戏来源: </span>
                  <span className="text-gray-700">{new URL(testGame.iframeUrl).hostname}</span>
                </div>
                <div>
                  <span className="font-medium">iframe限制状态: </span>
                  <span className="text-red-600">已知受限域名</span>
                </div>
                <div>
                  <span className="font-medium">处理方式: </span>
                  <span className="text-green-600">安全iframe + 智能错误处理</span>
                </div>
              </div>

              <div className="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded">
                <p className="text-sm text-yellow-800">
                  <strong>注意:</strong> 此测试游戏来自受限域名，预期会显示iframe限制错误。这是正常现象，用于测试错误处理机制。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
