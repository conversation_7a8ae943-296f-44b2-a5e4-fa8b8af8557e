#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动翻译脚本 - 将 en.json 翻译为多种语言
使用 Pollinations AI API 进行翻译
"""

import requests
import json
import os
import time
from typing import Dict, Any

# API 配置
API_URL = "https://text.pollinations.ai/openai"
API_HEADERS = {
    "Content-Type": "application/json",
    "Authorization": "Bearer D5iEptuNtJWrRFjC"
}

# 目标语言配置
LANGUAGES = {
    "ar": "阿拉伯语",
    "bg": "保加利亚语", 
    "bn": "孟加拉语",
    "cs": "捷克语",
    "da": "丹麦语",
    "de": "德语",
    "el": "希腊语",
    "es": "西班牙语",
    "et": "爱沙尼亚语",
    "fi": "芬兰语",
    "fr": "法语",
    "he": "希伯来语",
    "hi": "印地语",
    "hr": "克罗地亚语",
    "hu": "匈牙利语",
    "id": "印尼语",
    "it": "意大利语",
    "ja": "日语",
    "ko": "韩语",
    "lt": "立陶宛语",
    "lv": "拉脱维亚语",
    "ms": "马来语",
    "nl": "荷兰语",
    "no": "挪威语",
    "pl": "波兰语",
    "pt": "葡萄牙语",
    "pt-BR": "巴西葡萄牙语",
    "ro": "罗马尼亚语",
    "ru": "俄语",
    "sk": "斯洛伐克语",
    "sl": "斯洛文尼亚语",
    "sv": "瑞典语",
    "th": "泰语",
    "tr": "土耳其语",
    "uk": "乌克兰语",
    "vi": "越南语",
    "zh-CN": "中文简体",
    "zh-TW": "中文繁体"
}

def call_ai_api(prompt: str, max_retries: int = 3) -> str:
    """调用 AI API 进行翻译"""
    payload = {
        "model": "openai-fast",
        "messages": [
            {
                "role": "system", 
                "content": "你是一个专业的翻译专家。请将给定的JSON内容翻译为指定语言，保持JSON格式不变，只翻译值部分，不要翻译键名。确保翻译准确、自然、符合目标语言的表达习惯。"
            },
            {
                "role": "user", 
                "content": prompt
            }
        ],
        "seed": 101
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.post(API_URL, headers=API_HEADERS, json=payload, timeout=30)
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except requests.exceptions.RequestException as e:
            print(f"API 调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
            else:
                raise
        except (KeyError, json.JSONDecodeError) as e:
            print(f"API 响应解析失败: {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
            else:
                raise

def translate_json_content(content: Dict[Any, Any], target_language: str, language_name: str) -> Dict[Any, Any]:
    """翻译 JSON 内容"""
    print(f"正在翻译为 {language_name} ({target_language})...")
    
    # 将 JSON 转换为字符串
    json_str = json.dumps(content, ensure_ascii=False, indent=2)
    
    # 构建翻译提示
    prompt = f"""请将以下JSON内容翻译为{language_name}，保持JSON格式不变，只翻译值部分：

{json_str}

要求：
1. 保持JSON结构完全不变
2. 只翻译字符串值，不要翻译键名
3. 翻译要准确、自然、符合{language_name}的表达习惯
4. 游戏分类名称要使用常见的游戏术语
5. 返回完整的JSON格式

翻译后的JSON："""
    
    try:
        # 调用 AI API
        translated_text = call_ai_api(prompt)
        
        # 尝试解析返回的 JSON
        # 清理可能的markdown格式
        if "```json" in translated_text:
            translated_text = translated_text.split("```json")[1].split("```")[0].strip()
        elif "```" in translated_text:
            translated_text = translated_text.split("```")[1].strip()
        
        translated_json = json.loads(translated_text)
        return translated_json
        
    except json.JSONDecodeError as e:
        print(f"翻译结果JSON解析失败: {e}")
        print(f"原始响应: {translated_text[:500]}...")
        return None
    except Exception as e:
        print(f"翻译过程出错: {e}")
        return None

def main():
    """主函数"""
    # 检查源文件是否存在
    source_file = "messages/en.json"
    if not os.path.exists(source_file):
        print(f"错误: 源文件 {source_file} 不存在")
        return
    
    # 确保 messages 目录存在
    os.makedirs("messages", exist_ok=True)
    
    # 读取英文源文件
    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            source_content = json.load(f)
        print(f"成功读取源文件: {source_file}")
    except Exception as e:
        print(f"读取源文件失败: {e}")
        return
    
    # 统计信息
    total_languages = len(LANGUAGES)
    completed = 0
    failed = []
    
    print(f"开始翻译，目标语言数量: {total_languages}")
    print("-" * 50)
    
    # 逐个语言进行翻译
    for lang_code, lang_name in LANGUAGES.items():
        target_file = f"messages/{lang_code}.json"
        
        # 检查文件是否已存在
        if os.path.exists(target_file):
            print(f"跳过 {lang_name} ({lang_code}) - 文件已存在")
            completed += 1
            continue
        
        try:
            # 翻译内容
            translated_content = translate_json_content(source_content, lang_code, lang_name)
            
            if translated_content:
                # 保存翻译结果
                with open(target_file, 'w', encoding='utf-8') as f:
                    json.dump(translated_content, f, ensure_ascii=False, indent=2)
                
                print(f"✅ 成功: {lang_name} ({lang_code}) -> {target_file}")
                completed += 1
            else:
                print(f"❌ 失败: {lang_name} ({lang_code}) - 翻译失败")
                failed.append(f"{lang_name} ({lang_code})")
            
            # 添加延迟避免API限制
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 失败: {lang_name} ({lang_code}) - {e}")
            failed.append(f"{lang_name} ({lang_code})")
        
        print("-" * 30)
    
    # 输出统计结果
    print("\n" + "=" * 50)
    print("翻译完成统计:")
    print(f"总计语言: {total_languages}")
    print(f"成功翻译: {completed}")
    print(f"失败数量: {len(failed)}")
    
    if failed:
        print("\n失败的语言:")
        for lang in failed:
            print(f"  - {lang}")
        print("\n你可以重新运行脚本来重试失败的翻译")
    else:
        print("\n🎉 所有语言翻译完成！")

if __name__ == "__main__":
    main()
