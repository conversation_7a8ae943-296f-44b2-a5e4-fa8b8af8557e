import { getAllCategoriesServer, getAllGamesServer } from '@/lib/gameData';
import SmartGameGrid from '@/components/SmartGameGrid';
import DevTools from '@/components/DevTools';
import GameBackground from '@/components/GameBackground';
import GameHeader from '@/components/GameHeader';
import { getTranslations } from 'next-intl/server';
import SidebarCategoryFilter from '@/components/SidebarCategoryFilter';

export const runtime = 'edge';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ category?: string; search?: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'meta' });
  
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale: locale,
      siteName: 'AI Game Site'
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description')
    },
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/${locale}`
    }
  };
}

export default async function HomePage({ searchParams }: PageProps) {
  const { category, search } = await searchParams;
  const categories = getAllCategoriesServer();

  // 获取所有游戏
  const allGames = getAllGamesServer();

  // 根据分类和搜索条件过滤游戏
  let filteredGames = allGames;

  // 先按分类过滤
  if (category) {
    filteredGames = filteredGames.filter(game => game.category === category);
  }

  // 再按搜索关键词过滤
  if (search) {
    const searchTerm = search.toLowerCase();
    filteredGames = filteredGames.filter(game =>
      game.title.toLowerCase().includes(searchTerm) ||
      game.description.toLowerCase().includes(searchTerm) ||
      game.category.toLowerCase().includes(searchTerm)
    );
  }

  return (
    <div className="min-h-screen relative">
      {/* 动态背景 */}
      <GameBackground />

      {/* 游戏站Header */}
      <GameHeader />

      {/* 侧边栏分类过滤器 */}
      <SidebarCategoryFilter categories={categories} selectedCategory={category || null} />

      {/* 主要内容区域 - 动态响应侧边栏 */}
      <div className="relative lg:ml-80 pt-20 pb-8 transition-all duration-300" id="main-content">
        <div className="container mx-auto px-4 lg:px-8">
          {/* 搜索结果提示 */}
          {search && (
            <div className="mb-6 p-4 bg-white/10 backdrop-blur-md rounded-xl border border-white/20">
              <h2 className="text-xl font-bold text-white mb-2">
                搜索结果: &ldquo;{search}&rdquo;
              </h2>
              <p className="text-gray-300">
                找到 {filteredGames.length} 个游戏
              </p>
            </div>
          )}

          <SmartGameGrid
            games={filteredGames}
            forceMode={category || search ? undefined : 'optimized'} // 有筛选条件时显示所有结果
            showAll={!search && !category} // 没有搜索和分类条件时一次性显示所有游戏
          />
        </div>
      </div>

      {/* 开发工具 */}
      <DevTools gameCount={filteredGames.length} />
    </div>
  );
}